{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.insertSpaces": true, "editor.tabSize": 2, "files.encoding": "utf8", "files.eol": "\n", "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "[markdown]": {"files.trimTrailingWhitespace": false}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "cSpell.words": ["bumpp", "<PERSON><PERSON><PERSON>s", "chokidar", "composables", "devtool", "en<PERSON><PERSON>", "esbuild", "frontmatter", "lightningcss", "mdit", "prefetch", "preload", "slugify", "tinyglobby", "unmount", "upath", "v<PERSON><PERSON><PERSON>", "vuepress", "vueuse"], "typescript.tsdk": "node_modules/typescript/lib"}