# Contributing Guide

## Overview

This repository employs a [monorepo](https://en.wikipedia.org/wiki/Monorepo) setup with [pnpm workspaces](https://pnpm.io/workspaces), and hosts a number of associated but separated packages in the `packages` directory.

Core packages:

- `cli`: The VuePress command line interface (CLI) package. It will resolve user config file, and create VuePress app with `@vuepress/core`, then execute corresponding command.
- `client`: The VuePress client package. Provides the client entry, and exports types and composable utils that can be used in client side development.
- `core`: The VuePress core. Provides pure Node API to generate VuePress app, including page handling, plugin system and data preparation.
- `markdown`: The VuePress markdown package. Use `markdown-it` as the markdown parser and integrate some plugins to be used in VuePress.
- `shared`: Utilities that shared between node side and client side.
- `utils`: Utilities that should only be used in node side.

Wrapper of core packages:

- `vuepress`: A wrapper of the above packages, and provides `vuepress` command line tool. Users need to choose and install bundler and theme by themselves.

Bundler and related packages:

- `bundler-vite`: The VuePress bundler package with vite. Use vite to `dev` and `build` VuePress app that generated by `@vuepress/core`.
- `bundler-webpack`: The VuePress bundler package with webpack. Use webpack to `dev` and `build` VuePress app that generated by `@vuepress/core`.
- `bundlerutils`: Utilities for bundler packages.

## Development Setup

Pre-requirement:

- [Node.js](http://nodejs.org) **version 20.9.0+**

Clone the repo, and install dependencies:

```bash
corepack enable
pnpm install
```

Build source code:

```bash
pnpm build
```

### Tools

- [TypeScript](https://www.typescriptlang.org/) as the development language
- [ESLint](https://eslint.org/) + [Prettier](https://prettier.io/) for code linting and formatting
- [Vitest](https://vitest.dev/) for unit testing
- [Playwright](https://playwright.dev/) for end-to-end testing

### Scripts

#### `pnpm build`

The `build` script uses `tsup` to compile TypeScript source files to JavaScript dist files.

You may need to run this script first after your clone this repository, because the dist files are ignored by `.gitignore`.

#### `pnpm clean`

The `clean` script runs `clean` script in all packages, cleaning all the dist files and caches. In other words, it will remove all the files that generated by `build` scripts.

It's used before you want to re-build source files from a clean / initial state.

#### `pnpm format`

The `format` script uses Prettier to format all source files.

#### `pnpm lint`

The `lint` script uses ESLint to check all source files.

#### `pnpm test`

The `test` script uses Vitest to run unit testings, and uses Playwright to run end-to-end testings.

## End-to-end Testing

All end-to-end (e2e) tests are located in `e2e` directory.

You can also make use of the e2e site to test features during development. The source code of the e2e site is located in `e2e/docs`.

To run e2e tests, you need to change the working directory to `e2e` first:

```bash
cd e2e
```

### Using E2E Site

The e2e site is a normal VuePress setup, you can run `dev` and `build` commands in it:

```bash
# start dev server
pnpm docs:dev
# build and start preview server
pnpm docs:build
pnpm docs:serve
```

### Run E2E Tests

```bash
# run e2e tests in dev mode
pnpm e2e:dev
# run e2e tests in build mode
pnpm e2e:build
```

## Repositories

Current repository only maintains the core packages of VuePress. The following repositories are also part of the VuePress project.

### Documentation

VuePress official documentation is maintained in [vuepress/docs](https://github.com/vuepress/docs) repository.

### Plugins and Themes

VuePress official plugins and themes are maintained in [vuepress/ecosystem](https://github.com/vuepress/ecosystem) repository.
