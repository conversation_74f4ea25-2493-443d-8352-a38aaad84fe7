{"name": "@vuepress/e2e", "version": "2.0.0-rc.25", "private": true, "type": "module", "scripts": {"docs:build": "vuepress build docs --clean-cache --clean-temp", "docs:build:webpack": "cross-env E2E_BUNDLER=webpack pnpm docs:build", "docs:clean": "rimraf docs/.vuepress/.temp docs/.vuepress/.cache docs/.vuepress/dist", "docs:dev": "vuepress dev docs --clean-cache --clean-temp", "docs:dev:webpack": "cross-env E2E_BUNDLER=webpack pnpm docs:dev", "docs:serve": "serve -l 9080 docs/.vuepress/dist", "e2e:build": "cross-env E2E_COMMAND=build playwright test", "e2e:build:webpack": "cross-env E2E_COMMAND=build E2E_BUNDLER=webpack playwright test", "e2e:dev": "cross-env E2E_COMMAND=dev playwright test", "e2e:dev:webpack": "cross-env E2E_COMMAND=dev E2E_BUNDLER=webpack playwright test"}, "dependencies": {"@vuepress-e2e/conditional-exports": "file:./modules/conditional-exports", "@vuepress-e2e/style-exports": "file:./modules/style-exports", "@vuepress/bundler-vite": "workspace:*", "@vuepress/bundler-webpack": "workspace:*", "sass": "^1.91.0", "sass-embedded": "^1.91.0", "sass-loader": "^16.0.5", "vue": "catalog:", "vuepress": "workspace:*"}, "devDependencies": {"@playwright/test": "^1.55.0", "cross-env": "^10.0.0", "serve": "^14.2.4"}}