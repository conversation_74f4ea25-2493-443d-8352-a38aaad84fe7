# 系统架构

ThingsCloud 采用微服务架构设计，提供高可用、高扩展性的物联网平台服务。

## 整体架构

```mermaid
graph TB
    A[设备层] --> B[接入层]
    B --> C[服务层]
    C --> D[数据层]
    C --> E[应用层]
    
    subgraph 设备层
        A1[传感器]
        A2[控制器]
        A3[网关]
    end
    
    subgraph 接入层
        B1[MQTT Broker]
        B2[HTTP Gateway]
        B3[WebSocket Server]
    end
    
    subgraph 服务层
        C1[设备管理]
        C2[数据处理]
        C3[规则引擎]
        C4[消息路由]
    end
    
    subgraph 数据层
        D1[时序数据库]
        D2[关系数据库]
        D3[缓存]
    end
    
    subgraph 应用层
        E1[Web 控制台]
        E2[移动 App]
        E3[API 服务]
    end
```

## 核心组件

### 1. 设备接入层

负责处理各种设备的连接和通信：

- **MQTT Broker**: 处理 MQTT 协议设备
- **HTTP Gateway**: 处理 HTTP/HTTPS 请求
- **WebSocket Server**: 提供实时双向通信
- **协议适配器**: 支持自定义协议

### 2. 服务层

提供核心业务逻辑：

- **设备管理服务**: 设备注册、认证、状态管理
- **数据处理服务**: 数据清洗、转换、聚合
- **规则引擎**: 实时数据处理和告警
- **消息路由**: 消息分发和路由

### 3. 数据存储层

- **时序数据库**: 存储设备数据和指标
- **关系数据库**: 存储设备信息和配置
- **缓存系统**: 提高数据访问性能

## 技术栈

### 后端技术

- **Node.js**: 主要开发语言
- **TypeScript**: 类型安全
- **Express.js**: Web 框架
- **MongoDB**: 文档数据库
- **InfluxDB**: 时序数据库
- **Redis**: 缓存和消息队列

### 前端技术

- **Vue.js 3**: 前端框架
- **TypeScript**: 类型安全
- **Element Plus**: UI 组件库
- **ECharts**: 数据可视化
- **Vite**: 构建工具

### 基础设施

- **Docker**: 容器化部署
- **Kubernetes**: 容器编排
- **Nginx**: 反向代理和负载均衡
- **Prometheus**: 监控和告警

## 部署架构

### 单机部署

适合开发和测试环境：

```yaml
version: '3.8'
services:
  app:
    image: thingscloud/app:latest
    ports:
      - "3000:3000"
  
  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
  
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
```

### 集群部署

适合生产环境，支持高可用和水平扩展。

## 安全架构

- **设备认证**: 支持证书、密钥等多种认证方式
- **数据加密**: 传输和存储数据加密
- **访问控制**: 基于角色的权限管理
- **审计日志**: 完整的操作审计记录
