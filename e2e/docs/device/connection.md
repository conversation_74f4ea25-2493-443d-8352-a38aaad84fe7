# 设备连接

本章介绍如何将各种类型的设备连接到 ThingsCloud 平台。

## 支持的协议

ThingsCloud 支持多种主流的物联网通信协议：

### MQTT

MQTT 是最常用的物联网协议，具有轻量级、低功耗的特点。

#### 连接参数

```javascript
const mqttOptions = {
  host: 'mqtt.thingscloud.xyz',
  port: 1883,
  clientId: 'device_001',
  username: 'your_username',
  password: 'your_password',
  keepalive: 60,
  clean: true
};
```

#### 主题规范

- **数据上报**: `devices/{deviceId}/telemetry`
- **属性上报**: `devices/{deviceId}/attributes`
- **命令接收**: `devices/{deviceId}/commands`

#### 示例代码

```javascript
const mqtt = require('mqtt');

const client = mqtt.connect('mqtt://mqtt.thingscloud.xyz', {
  clientId: 'device_001',
  username: 'your_username',
  password: 'your_password'
});

client.on('connect', () => {
  console.log('设备已连接');
  
  // 订阅命令主题
  client.subscribe('devices/device_001/commands');
  
  // 上报数据
  setInterval(() => {
    const data = {
      temperature: Math.random() * 30 + 10,
      humidity: Math.random() * 100,
      timestamp: Date.now()
    };
    
    client.publish('devices/device_001/telemetry', JSON.stringify(data));
  }, 5000);
});

client.on('message', (topic, message) => {
  console.log('收到命令:', message.toString());
});
```

### HTTP/HTTPS

适合间歇性数据上报的场景。

#### API 端点

- **数据上报**: `POST /api/v1/devices/{deviceId}/telemetry`
- **属性上报**: `POST /api/v1/devices/{deviceId}/attributes`

#### 示例代码

```javascript
const axios = require('axios');

const deviceId = 'device_001';
const apiKey = 'your_api_key';

// 上报数据
async function reportData() {
  try {
    const response = await axios.post(
      `https://api.thingscloud.xyz/v1/devices/${deviceId}/telemetry`,
      {
        temperature: 25.6,
        humidity: 60.2,
        timestamp: Date.now()
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('数据上报成功:', response.data);
  } catch (error) {
    console.error('数据上报失败:', error.message);
  }
}

// 每分钟上报一次数据
setInterval(reportData, 60000);
```

### WebSocket

适合需要实时双向通信的场景。

```javascript
const WebSocket = require('ws');

const ws = new WebSocket('wss://ws.thingscloud.xyz/devices/device_001', {
  headers: {
    'Authorization': 'Bearer your_api_key'
  }
});

ws.on('open', () => {
  console.log('WebSocket 连接已建立');
  
  // 发送数据
  ws.send(JSON.stringify({
    type: 'telemetry',
    data: {
      temperature: 25.6,
      humidity: 60.2
    }
  }));
});

ws.on('message', (data) => {
  const message = JSON.parse(data);
  console.log('收到消息:', message);
});
```

## 设备认证

### API Key 认证

最简单的认证方式，适合测试和开发环境。

```javascript
const headers = {
  'Authorization': 'Bearer your_api_key'
};
```

### 证书认证

更安全的认证方式，适合生产环境。

```javascript
const fs = require('fs');
const mqtt = require('mqtt');

const options = {
  key: fs.readFileSync('device-key.pem'),
  cert: fs.readFileSync('device-cert.pem'),
  ca: fs.readFileSync('ca-cert.pem'),
  protocol: 'mqtts',
  port: 8883
};

const client = mqtt.connect('mqtts://mqtt.thingscloud.xyz', options);
```

## 数据格式

### 遥测数据

```json
{
  "temperature": 25.6,
  "humidity": 60.2,
  "pressure": 1013.25,
  "timestamp": 1640995200000
}
```

### 属性数据

```json
{
  "firmware_version": "1.2.3",
  "battery_level": 85,
  "signal_strength": -65
}
```

### 事件数据

```json
{
  "event_type": "alarm",
  "severity": "high",
  "message": "温度超过阈值",
  "timestamp": 1640995200000
}
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证认证信息
   - 确认服务器地址和端口

2. **数据上报失败**
   - 检查数据格式
   - 验证设备权限
   - 查看错误日志

3. **消息丢失**
   - 检查 QoS 设置
   - 确认网络稳定性
   - 查看消息队列状态

### 调试工具

- **MQTT 客户端**: MQTT.fx, MQTTX
- **HTTP 客户端**: Postman, curl
- **网络抓包**: Wireshark
- **日志查看**: 平台控制台
