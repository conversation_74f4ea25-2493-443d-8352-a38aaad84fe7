# Configuration

VuePress sites are configured via a `.vuepress/config.js` file in your docs directory.

## Basic Config

The most important thing to set is the `title` and `description` of your site:

```js
module.exports = {
  title: 'Hello VuePress',
  description: 'Just playing around'
}
```

## Theme Configuration

You can configure the default theme by adding a `themeConfig` property to your config file:

```js
module.exports = {
  themeConfig: {
    // Navigation
    nav: [
      { text: 'Home', link: '/' },
      { text: 'Guide', link: '/guide/' },
      { text: 'External', link: 'https://google.com' },
    ],
    
    // Sidebar
    sidebar: [
      '/',
      '/page-a',
      ['/page-b', 'Explicit link text']
    ]
  }
}
```

## Site Metadata

### title

- Type: `string`
- Default: `undefined`

Title for the site. This will be the prefix for all page titles, and displayed in the navbar in the default theme.

### description

- Type: `string`
- Default: `undefined`

Description for the site. This will render as a `<meta>` tag in the page HTML.

### head

- Type: `Array`
- Default: `[]`

Extra tags to inject into the page HTML `<head>`. Each tag can be specified in the form of `[tagName, { attrName: attrValue }, innerHTML?]`.

```js
module.exports = {
  head: [
    ['link', { rel: 'icon', href: '/logo.png' }],
    ['meta', { name: 'theme-color', content: '#3eaf7c' }],
    ['meta', { name: 'apple-mobile-web-app-capable', content: 'yes' }],
    ['meta', { name: 'apple-mobile-web-app-status-bar-style', content: 'black' }]
  ]
}
```

## Theming

### theme

- Type: `string`
- Default: `undefined`

Specify this to use a custom theme.

```js
module.exports = {
  theme: 'awesome'
}
```

### themeConfig

- Type: `Object`
- Default: `{}`

Provide config options to the used theme. The options will vary depending on the theme you are using.

## Plugin

### plugins

- Type: `Object | Array`
- Default: `undefined`

See [Plugin > Using a Plugin](../plugin/using-a-plugin.md) for details.

## Markdown

### markdown.lineNumbers

- Type: `boolean`
- Default: `undefined`

Whether to show line numbers to the left of each code blocks.

### markdown.anchor

- Type: `Object`
- Default: `{ permalink: true, permalinkBefore: true, permalinkSymbol: '#' }`

Options for [markdown-it-anchor](https://github.com/valeriangalliat/markdown-it-anchor).

### markdown.toc

- Type: `Object`
- Default: `{ includeLevel: [2, 3] }`

Options for [markdown-it-table-of-contents](https://github.com/Oktavilla/markdown-it-table-of-contents).

### markdown.config

- Type: `Function`
- Default: `undefined`

A function to apply additional plugins to the [markdown-it](https://github.com/markdown-it/markdown-it) instance used to render source files.

```js
module.exports = {
  markdown: {
    config: md => {
      md.use(require('markdown-it-xxx'))
    }
  }
}
```

## Build Pipeline

### configureWebpack

- Type: `Object | Function`
- Default: `undefined`

Modify the internal webpack config. If the value is an Object, it will be merged into the final config using [webpack-merge](https://github.com/survivejs/webpack-merge); If the value is a function, it will receive the config as the 1st argument and an `isServer` flag as the 2nd argument.

```js
module.exports = {
  configureWebpack: (config, isServer) => {
    if (!isServer) {
      // mutate the config for client
    }
  }
}
```

### chainWebpack

- Type: `Function`
- Default: `undefined`

Modify the internal webpack config with [webpack-chain](https://github.com/mozilla-neutrino/webpack-chain).

```js
module.exports = {
  chainWebpack: (config, isServer) => {
    // config is an instance of ChainableConfig
  }
}
```
