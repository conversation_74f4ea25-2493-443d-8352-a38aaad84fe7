<script setup lang="ts">
import { Content, useSiteData, usePageData, useRouteLocale } from 'vuepress/client'
import { computed } from 'vue'

const siteData = useSiteData()
const pageData = usePageData()
const routeLocale = useRouteLocale()

// 导航菜单配置
const navItems = [
  { text: '介绍', link: '/' },
  {
    text: 'ThingsCloud 设计',
    children: [
      { text: '系统架构', link: '/design/architecture.html' },
      { text: '核心概念', link: '/design/concepts.html' }
    ]
  },
  {
    text: '设备',
    children: [
      { text: '设备连接', link: '/device/connection.html' },
      { text: '设备管理', link: '/device/management.html' }
    ]
  },
  {
    text: '数据处理',
    children: [
      { text: '数据采集', link: '/data/collection.html' },
      { text: '数据分析', link: '/data/analysis.html' }
    ]
  },
  {
    text: 'API 参考',
    children: [
      { text: 'REST API', link: '/api/rest.html' },
      { text: 'WebSocket', link: '/api/websocket.html' }
    ]
  },
  { text: '联系我们', link: '/contact/' },
  { text: '博客', link: '/blog/' }
]

// 侧边栏配置
const sidebarItems = [
  {
    text: '介绍',
    children: [
      { text: '首页', link: '/' },
      { text: '快速开始', link: '/getting-started.html' }
    ]
  },
  {
    text: 'ThingsCloud 设计',
    children: [
      { text: '系统架构', link: '/design/architecture.html' },
      { text: '核心概念', link: '/design/concepts.html' }
    ]
  },
  {
    text: '设备管理',
    children: [
      { text: '设备连接', link: '/device/connection.html' },
      { text: '设备管理', link: '/device/management.html' },
      { text: '协议支持', link: '/device/protocols.html' }
    ]
  },
  {
    text: '数据处理',
    children: [
      { text: '数据采集', link: '/data/collection.html' },
      { text: '数据分析', link: '/data/analysis.html' },
      { text: '数据可视化', link: '/data/visualization.html' }
    ]
  },
  {
    text: 'API 参考',
    children: [
      { text: 'REST API', link: '/api/rest.html' },
      { text: 'WebSocket', link: '/api/websocket.html' },
      { text: '示例代码', link: '/api/examples.html' }
    ]
  }
]

const currentPath = computed(() => pageData.value.path)
</script>

<template>
  <div class="thingscloud-theme">
    <!-- 顶部导航栏 -->
    <header class="navbar">
      <div class="navbar-container">
        <div class="navbar-brand">
          <RouterLink to="/" class="brand-link">
            <span class="brand-logo">🌐</span>
            <span class="brand-text">ThingsCloud 使用文档</span>
          </RouterLink>
        </div>

        <nav class="navbar-nav">
          <div v-for="item in navItems" :key="item.text" class="nav-item">
            <RouterLink v-if="!item.children" :to="item.link" class="nav-link">
              {{ item.text }}
            </RouterLink>
            <div v-else class="nav-dropdown">
              <span class="nav-link dropdown-toggle">{{ item.text }}</span>
              <div class="dropdown-menu">
                <RouterLink
                  v-for="child in item.children"
                  :key="child.text"
                  :to="child.link"
                  class="dropdown-item"
                >
                  {{ child.text }}
                </RouterLink>
              </div>
            </div>
          </div>
        </nav>

        <div class="navbar-actions">
          <div class="language-selector">
            <span>语言</span>
            <div class="language-menu">
              <RouterLink
                v-for="[key, value] in Object.entries(siteData.locales)"
                :key="key"
                :to="key"
                class="language-item"
              >
                {{ value.lang }}
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="main-container">
      <!-- 侧边栏 -->
      <aside class="sidebar">
        <div class="sidebar-content">
          <div v-for="group in sidebarItems" :key="group.text" class="sidebar-group">
            <h3 class="sidebar-group-title">{{ group.text }}</h3>
            <ul class="sidebar-links">
              <li v-for="item in group.children" :key="item.text">
                <RouterLink
                  :to="item.link"
                  class="sidebar-link"
                  :class="{ active: currentPath === item.link }"
                >
                  {{ item.text }}
                </RouterLink>
              </li>
            </ul>
          </div>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="main-content">
        <div class="content-wrapper">
          <Content />
        </div>
      </main>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.thingscloud-theme {
  min-height: 100vh;
  background-color: #0d1117;
  color: #e6edf3;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部导航栏 */
.navbar {
  background-color: #161b22;
  border-bottom: 1px solid #30363d;
  position: sticky;
  top: 0;
  z-index: 100;

  .navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
  }

  .navbar-brand {
    .brand-link {
      display: flex;
      align-items: center;
      text-decoration: none;
      color: #e6edf3;
      font-weight: 600;
      font-size: 18px;

      .brand-logo {
        margin-right: 8px;
        font-size: 24px;
      }
    }
  }

  .navbar-nav {
    display: flex;
    align-items: center;
    gap: 32px;

    .nav-item {
      position: relative;

      .nav-link {
        color: #e6edf3;
        text-decoration: none;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.2s;

        &:hover {
          background-color: #21262d;
          color: #58a6ff;
        }
      }

      .nav-dropdown {
        position: relative;

        .dropdown-toggle {
          cursor: pointer;

          &:after {
            content: ' ▼';
            font-size: 12px;
            margin-left: 4px;
          }
        }

        .dropdown-menu {
          position: absolute;
          top: 100%;
          left: 0;
          background-color: #21262d;
          border: 1px solid #30363d;
          border-radius: 6px;
          padding: 8px 0;
          min-width: 160px;
          opacity: 0;
          visibility: hidden;
          transform: translateY(-10px);
          transition: all 0.2s;

          .dropdown-item {
            display: block;
            padding: 8px 16px;
            color: #e6edf3;
            text-decoration: none;
            transition: background-color 0.2s;

            &:hover {
              background-color: #30363d;
              color: #58a6ff;
            }
          }
        }

        &:hover .dropdown-menu {
          opacity: 1;
          visibility: visible;
          transform: translateY(0);
        }
      }
    }
  }

  .navbar-actions {
    .language-selector {
      position: relative;
      color: #e6edf3;
      cursor: pointer;

      .language-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: #21262d;
        border: 1px solid #30363d;
        border-radius: 6px;
        padding: 8px 0;
        min-width: 120px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.2s;

        .language-item {
          display: block;
          padding: 8px 16px;
          color: #e6edf3;
          text-decoration: none;
          transition: background-color 0.2s;

          &:hover {
            background-color: #30363d;
            color: #58a6ff;
          }
        }
      }

      &:hover .language-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }
    }
  }
}

/* 主容器 */
.main-container {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 64px);
}

/* 侧边栏 */
.sidebar {
  width: 280px;
  background-color: #0d1117;
  border-right: 1px solid #30363d;

  .sidebar-content {
    padding: 24px 0;
    position: sticky;
    top: 64px;
    max-height: calc(100vh - 64px);
    overflow-y: auto;
  }

  .sidebar-group {
    margin-bottom: 32px;

    .sidebar-group-title {
      color: #f0f6fc;
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 16px 24px;
      padding-bottom: 8px;
      border-bottom: 1px solid #30363d;
    }

    .sidebar-links {
      list-style: none;
      margin: 0;
      padding: 0;

      li {
        margin: 0;

        .sidebar-link {
          display: block;
          padding: 8px 24px;
          color: #e6edf3;
          text-decoration: none;
          transition: all 0.2s;
          border-left: 3px solid transparent;

          &:hover {
            background-color: #161b22;
            color: #58a6ff;
            border-left-color: #58a6ff;
          }

          &.active {
            background-color: #1f2937;
            color: #58a6ff;
            border-left-color: #58a6ff;
            font-weight: 500;
          }
        }
      }
    }
  }
}

/* 主内容区域 */
.main-content {
  flex: 1;
  background-color: #0d1117;

  .content-wrapper {
    padding: 32px 48px;
    max-width: 800px;

    :deep(h1) {
      color: #f0f6fc;
      font-size: 32px;
      font-weight: 600;
      margin: 0 0 24px 0;
      padding-bottom: 16px;
      border-bottom: 1px solid #30363d;
    }

    :deep(h2) {
      color: #f0f6fc;
      font-size: 24px;
      font-weight: 600;
      margin: 32px 0 16px 0;
    }

    :deep(h3) {
      color: #f0f6fc;
      font-size: 20px;
      font-weight: 600;
      margin: 24px 0 12px 0;
    }

    :deep(p) {
      color: #e6edf3;
      line-height: 1.6;
      margin: 0 0 16px 0;
    }

    :deep(a) {
      color: #58a6ff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    :deep(code) {
      background-color: #161b22;
      color: #f85149;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    }

    :deep(pre) {
      background-color: #161b22;
      border: 1px solid #30363d;
      border-radius: 6px;
      padding: 16px;
      overflow-x: auto;
      margin: 16px 0;

      code {
        background: none;
        color: #e6edf3;
        padding: 0;
      }
    }

    :deep(ul), :deep(ol) {
      color: #e6edf3;
      padding-left: 24px;

      li {
        margin: 8px 0;
        line-height: 1.6;
      }
    }

    :deep(blockquote) {
      border-left: 4px solid #30363d;
      padding: 0 16px;
      margin: 16px 0;
      color: #8b949e;
    }

    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 16px 0;

      th, td {
        border: 1px solid #30363d;
        padding: 8px 12px;
        text-align: left;
      }

      th {
        background-color: #161b22;
        color: #f0f6fc;
        font-weight: 600;
      }

      td {
        color: #e6edf3;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #30363d;

    .sidebar-content {
      position: static;
      max-height: none;
      padding: 16px 0;
    }
  }

  .main-content .content-wrapper {
    padding: 24px 16px;
  }

  .navbar .navbar-container {
    padding: 0 16px;
  }

  .navbar .navbar-nav {
    display: none;
  }
}
</style>
