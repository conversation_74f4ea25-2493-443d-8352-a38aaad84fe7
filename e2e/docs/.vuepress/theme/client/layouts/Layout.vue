<script setup lang="ts">
import { Content, useSiteData } from 'vuepress/client'

const siteData = useSiteData()
</script>

<template>
  <div class="vuepress-theme">
    <!-- 顶部导航栏 -->
    <header class="navbar">
      <div class="navbar-container">
        <div class="navbar-brand">
          <RouterLink to="/" class="brand-link">
            <span class="brand-logo">📚</span>
            <span class="brand-text">{{ siteData.title }}</span>
          </RouterLink>
        </div>

        <nav class="navbar-nav">
          <RouterLink to="/" class="nav-link">Home</RouterLink>
          <RouterLink to="/getting-started.html" class="nav-link">Getting Started</RouterLink>
          <RouterLink to="/guide/" class="nav-link">Guide</RouterLink>
          <RouterLink to="/config/" class="nav-link">Config</RouterLink>
        </nav>

        <div class="navbar-actions">
          <div class="language-selector">
            <span>Languages</span>
            <div class="language-menu">
              <RouterLink
                v-for="[key, value] in Object.entries(siteData.locales)"
                :key="key"
                :to="key"
                class="language-item"
              >
                {{ value.lang }}
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
      <div class="content-wrapper">
        <Content />
      </div>
    </main>
  </div>
</template>

<style lang="scss" scoped>
.vuepress-theme {
  min-height: 100vh;
  background-color: #ffffff;
  color: #2c3e50;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部导航栏 */
.navbar {
  background-color: #ffffff;
  border-bottom: 1px solid #eaecef;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);

  .navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
  }

  .navbar-brand {
    .brand-link {
      display: flex;
      align-items: center;
      text-decoration: none;
      color: #2c3e50;
      font-weight: 600;
      font-size: 18px;

      .brand-logo {
        margin-right: 8px;
        font-size: 24px;
      }
    }
  }

  .navbar-nav {
    display: flex;
    align-items: center;
    gap: 32px;

    .nav-link {
      color: #2c3e50;
      text-decoration: none;
      padding: 8px 12px;
      border-radius: 6px;
      transition: all 0.2s;

      &:hover {
        background-color: #f8f9fa;
        color: #3eaf7c;
      }

      &.router-link-active {
        color: #3eaf7c;
        font-weight: 600;
      }
    }
  }

  .navbar-actions {
    .language-selector {
      position: relative;
      color: #2c3e50;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f8f9fa;
      }

      .language-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: #ffffff;
        border: 1px solid #eaecef;
        border-radius: 6px;
        padding: 8px 0;
        min-width: 120px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.2s;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

        .language-item {
          display: block;
          padding: 8px 16px;
          color: #2c3e50;
          text-decoration: none;
          transition: background-color 0.2s;

          &:hover {
            background-color: #f8f9fa;
            color: #3eaf7c;
          }
        }
      }

      &:hover .language-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }
    }
  }
}

/* 主内容区域 */
.main-content {
  background-color: #ffffff;

  .content-wrapper {
    max-width: 800px;
    margin: 0 auto;
    padding: 32px 24px;

    :deep(h1) {
      color: #2c3e50;
      font-size: 32px;
      font-weight: 600;
      margin: 0 0 24px 0;
      padding-bottom: 16px;
      border-bottom: 1px solid #eaecef;
    }

    :deep(h2) {
      color: #2c3e50;
      font-size: 24px;
      font-weight: 600;
      margin: 32px 0 16px 0;
    }

    :deep(h3) {
      color: #2c3e50;
      font-size: 20px;
      font-weight: 600;
      margin: 24px 0 12px 0;
    }

    :deep(p) {
      color: #2c3e50;
      line-height: 1.7;
      margin: 0 0 16px 0;
    }

    :deep(a) {
      color: #3eaf7c;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    :deep(code) {
      background-color: #f8f8f8;
      color: #e96900;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    }

    :deep(pre) {
      background-color: #f8f8f8;
      border: 1px solid #eaecef;
      border-radius: 6px;
      padding: 16px;
      overflow-x: auto;
      margin: 16px 0;

      code {
        background: none;
        color: #2c3e50;
        padding: 0;
      }
    }

    :deep(ul), :deep(ol) {
      color: #2c3e50;
      padding-left: 24px;

      li {
        margin: 8px 0;
        line-height: 1.7;
      }
    }

    :deep(blockquote) {
      border-left: 4px solid #3eaf7c;
      padding: 0 16px;
      margin: 16px 0;
      color: #6a737d;
      background-color: #f8f9fa;
    }

    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 16px 0;

      th, td {
        border: 1px solid #eaecef;
        padding: 8px 12px;
        text-align: left;
      }

      th {
        background-color: #f8f9fa;
        color: #2c3e50;
        font-weight: 600;
      }

      td {
        color: #2c3e50;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar .navbar-container {
    padding: 0 16px;
  }

  .navbar .navbar-nav {
    gap: 16px;
  }

  .main-content .content-wrapper {
    padding: 24px 16px;
  }
}
</style>
