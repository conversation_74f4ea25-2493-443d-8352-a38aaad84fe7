* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  background-color: #0d1117;
  color: #e6edf3;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #161b22;
}

::-webkit-scrollbar-thumb {
  background: #30363d;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #484f58;
}

/* 选择文本样式 */
::selection {
  background-color: #58a6ff;
  color: #ffffff;
}

/* 链接默认样式 */
a {
  color: #58a6ff;
  text-decoration: none;
  transition: color 0.2s;
}

a:hover {
  color: #79c0ff;
  text-decoration: underline;
}

/* 代码块样式 */
code {
  font-family: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', <PERSON><PERSON>, 'Courier New', monospace;
}

/* 表单元素样式 */
input, textarea, select {
  background-color: #21262d;
  border: 1px solid #30363d;
  color: #e6edf3;
  border-radius: 6px;
  padding: 8px 12px;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: #58a6ff;
  box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
}

/* 按钮样式 */
button {
  background-color: #238636;
  border: 1px solid #238636;
  color: #ffffff;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

button:hover {
  background-color: #2ea043;
  border-color: #2ea043;
}

button:active {
  background-color: #1a7f37;
  border-color: #1a7f37;
}

/* 移除旧的 e2e-theme 样式 */
.e2e-theme {
  padding: 0;
}
