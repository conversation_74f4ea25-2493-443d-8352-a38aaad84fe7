* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  background-color: #ffffff;
  color: #2c3e50;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f8f9fa;
}

::-webkit-scrollbar-thumb {
  background: #dee2e6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
}

/* 选择文本样式 */
::selection {
  background-color: #3eaf7c;
  color: #ffffff;
}

/* 链接默认样式 */
a {
  color: #3eaf7c;
  text-decoration: none;
  transition: color 0.2s;
}

a:hover {
  color: #4abf8a;
  text-decoration: underline;
}

/* 代码块样式 */
code {
  font-family: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', <PERSON><PERSON>, 'Courier New', monospace;
}

/* 表单元素样式 */
input, textarea, select {
  background-color: #ffffff;
  border: 1px solid #eaecef;
  color: #2c3e50;
  border-radius: 6px;
  padding: 8px 12px;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: #3eaf7c;
  box-shadow: 0 0 0 3px rgba(62, 175, 124, 0.1);
}

/* 按钮样式 */
button {
  background-color: #3eaf7c;
  border: 1px solid #3eaf7c;
  color: #ffffff;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

button:hover {
  background-color: #4abf8a;
  border-color: #4abf8a;
}

button:active {
  background-color: #369970;
  border-color: #369970;
}
