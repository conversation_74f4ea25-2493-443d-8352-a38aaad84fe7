import process from 'node:process'
import { viteBundler } from '@vuepress/bundler-vite'
import { webpackBundler } from '@vuepress/bundler-webpack'
import { defineUserConfig } from 'vuepress'
import { path } from 'vuepress/utils'
import { fooPlugin } from './plugins/foo/fooPlugin.js'
import { e2eTheme } from './theme/node/e2eTheme.js'

const E2E_BASE = (process.env.E2E_BASE ?? '/') as '/' | `/${string}/`
const E2E_BUNDLER = process.env.E2E_BUNDLER ?? 'vite'

export default defineUserConfig({
  base: E2E_BASE,

  dest: path.join(__dirname, 'dist', E2E_BASE),

  port: 9080,

  head: [
    ['link', { rel: 'icon', href: '/favicon.ico' }],
  ],

  locales: {
    '/': {
      lang: 'zh-CN',
      title: 'ThingsCloud 使用文档',
      description: '物联网平台开发文档，提供设备接入、数据处理、可视化等完整解决方案',
    },
    '/en/': {
      lang: 'en-US',
      title: 'ThingsCloud Documentation',
      description: 'IoT platform development documentation with device access, data processing, and visualization solutions',
    },
  },

  markdown: {
    assets: {
      absolutePathPrependBase: E2E_BUNDLER === 'webpack',
    },
  },

  bundler:
    E2E_BUNDLER === 'webpack'
      ? webpackBundler()
      : viteBundler({
          viteOptions: {
            optimizeDeps: {
              include: ['@vuepress-e2e/conditional-exports'],
            },
          },
        }),

  theme: e2eTheme(),

  extendsPage: (page) => {
    if (page.path === '/page-data/route-meta.html') {
      page.routeMeta = {
        a: 1,
        b: 2,
        ...page.routeMeta,
      }
    }
  },

  plugins: [fooPlugin],

  // The alias entries are intentionally ordered by key length to ensure
  // that more specific aliases (e.g., '@dir/a.js') take precedence over
  // less specific ones (e.g., '@dir'). Do not reorder these entries.
  alias: {
    '@dir/a.js': path.resolve(__dirname, '../../modules/dir2/a.js'),
    '@dir': path.resolve(__dirname, '../../modules/dir1'),
    '@dir/b.js': path.resolve(__dirname, '../../modules/dir2/b.js'),
  },
})
