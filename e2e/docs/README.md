# VuePress Documentation

Welcome to VuePress! This is a Vue-powered static site generator.

## What is VuePress?

VuePress is composed of two parts: a minimalistic static site generator with a Vue-powered theming system and Plugin API, and a default theme optimized for writing technical documentation.

## Features

- **Vue-Powered**: Enjoy the dev experience of Vue + webpack, use Vue components in markdown, and develop custom themes with Vue.
- **Built-in Markdown Extensions**: Headers, table of contents, syntax highlighting, line highlighting, line numbers, import code snippets, etc.
- **Plugin System**: Flexible plugin system to extend or customize your site.

## Getting Started

1. **Installation**
   ```bash
   npm install -D vuepress@next
   ```

2. **Create your first document**
   ```bash
   mkdir docs && echo '# Hello VuePress' > docs/README.md
   ```

3. **Start writing**
   ```bash
   npx vuepress dev docs
   ```

## Quick Links

- [Guide](/guide/)
- [Config Reference](/config/)
- [Plugin Reference](/plugin/)
- [Theme Reference](/theme/)

## Community

- [GitHub](https://github.com/vuepress/vuepress-next)
- [Discord](https://discord.gg/HBherRA)
- [Awesome VuePress](https://github.com/vuepress/awesome-vuepress)
