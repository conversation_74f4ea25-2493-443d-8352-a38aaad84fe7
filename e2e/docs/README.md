---
home: true
title: ThingsCloud 使用文档
heroImage: /logo.png
heroText: ThingsCloud 使用文档
tagline: 物联网平台开发文档，提供设备接入、数据处理、可视化等完整解决方案
actions:
  - text: 快速开始
    link: /getting-started.html
    type: primary
  - text: 项目简介
    link: /guide/
    type: secondary
features:
  - title: 🚀 快速接入
    details: 支持多种设备协议，快速实现设备接入和管理
  - title: 📊 数据分析
    details: 强大的数据处理和分析能力，实时监控设备状态
  - title: 🎨 可视化
    details: 丰富的图表组件，轻松构建数据可视化界面
  - title: 🔧 API 丰富
    details: 完整的 REST API 和 WebSocket 接口
  - title: 🌐 多语言支持
    details: 支持多种编程语言的 SDK
  - title: 📱 移动端支持
    details: 提供移动端 App 和响应式 Web 界面
footer: MIT Licensed | Copyright © 2024 ThingsCloud
---

## 7 分钟了解 ThingsCloud

ThingsCloud 可以让大家快速、轻松、高效地进行物联网应用开发。它提供了完整的设备接入、数据处理、可视化等解决方案。

现在我们 ThingsCloud，企业级大大的物联网应用内容有所的功能，节省开发工作量，降低开发成本，提高开发效率。

### 主要特性

- **官网**: https://www.thingscloud.xyz/ ↗
- **控制台**: https://console.thingscloud.xyz/ ↗
- **文档**: https://www.thingscloud.xyz/docs/tutorials/ ↗
- **使用文档**: https://www.thingscloud.xyz/docs ↗
- **常用 API**: https://www.thingscloud.xyz/docs/guide/faq ↗
- **设备接入**: https://github.bilibili.com/B5354/444 ↗

### 快速开始

1. **注册账号**: 访问 ThingsCloud 控制台注册账号
2. **创建项目**: 在控制台创建新的物联网项目
3. **设备接入**: 根据文档配置设备接入
4. **数据处理**: 配置数据处理规则
5. **可视化**: 创建数据可视化界面
