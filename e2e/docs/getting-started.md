# 快速开始

欢迎使用 ThingsCloud 物联网平台！本指南将帮助您快速上手。

## 前置条件

在开始之前，请确保您具备以下条件：

- 注册 ThingsCloud 账号
- 基本的编程知识
- 了解物联网基本概念

## 第一步：创建项目

1. 登录 [ThingsCloud 控制台](https://console.thingscloud.xyz/)
2. 点击"创建项目"按钮
3. 填写项目基本信息
4. 选择合适的项目模板

## 第二步：设备接入

### 支持的协议

ThingsCloud 支持多种设备接入协议：

- **MQTT**: 轻量级消息传输协议
- **HTTP/HTTPS**: 标准 Web 协议
- **CoAP**: 受限应用协议
- **WebSocket**: 实时双向通信

### 设备注册

```javascript
// 使用 JavaScript SDK 注册设备
const device = await thingscloud.devices.create({
  name: '温度传感器01',
  type: 'temperature_sensor',
  protocol: 'mqtt'
});
```

## 第三步：数据上报

设备注册成功后，可以开始上报数据：

```javascript
// 上报温度数据
await device.publish('temperature', {
  value: 25.6,
  unit: '°C',
  timestamp: Date.now()
});
```

## 第四步：数据可视化

在控制台中创建数据可视化界面：

1. 进入"可视化"模块
2. 创建新的仪表板
3. 添加图表组件
4. 配置数据源

## 下一步

- 了解 [设备管理](/device/management.html)
- 学习 [数据处理](/data/collection.html)
- 查看 [API 文档](/api/rest.html)
