# Guide

This is the guide section of your VuePress documentation.

## Introduction

VuePress is a minimalistic static site generator with a Vue-powered theming system and Plugin API. It was created to support the documentation needs of <PERSON>ue's own sub projects.

## How It Works

A VuePress site is in fact a SPA powered by Vue, Vue Router and webpack. If you've used Vue before, you will notice the familiar development experience when you are writing or developing custom themes (you can even use Vue DevTools to debug your custom theme!).

During the build, we create a server-rendered version of the app and render the corresponding HTML for each route. This approach is inspired by <PERSON><PERSON><PERSON>'s nuxt generate command and other projects in the Vue ecosystem.

## Features

- **Built-in Markdown Extensions**: Headers, table of contents, syntax highlighting, line highlighting, line numbers, import code snippets, etc.
- **Vue in Markdown**: Use Vue components in markdown, and develop custom themes with Vue.
- **Plugin System**: Flexible plugin system to extend or customize your site.
- **Default Theme**: A responsive default theme with optional homepage, optional navbar, sidebar, etc.

## Directory Structure

VuePress follows the principle of "Convention over Configuration". The recommended directory structure is as follows:

```
.
├── docs
│   ├── .vuepress
│   │   ├── components
│   │   ├── theme
│   │   │   └── Layout.vue
│   │   ├── public
│   │   ├── styles
│   │   │   ├── index.styl
│   │   │   └── palette.styl
│   │   ├── templates
│   │   │   ├── dev.html
│   │   │   └── ssr.html
│   │   ├── config.js
│   │   └── enhanceApp.js
│   ├── README.md
│   ├── guide
│   │   └── README.md
│   └── config.md
└── package.json
```

## Getting Started

1. **Create a new directory and navigate into it**
   ```bash
   mkdir vuepress-starter && cd vuepress-starter
   ```

2. **Initialize with your preferred package manager**
   ```bash
   git init
   npm init
   ```

3. **Install VuePress locally**
   ```bash
   npm install -D vuepress@next
   ```

4. **Create your first document**
   ```bash
   mkdir docs && echo '# Hello VuePress' > docs/README.md
   ```

5. **Add some scripts to package.json**
   ```json
   {
     "scripts": {
       "docs:dev": "vuepress dev docs",
       "docs:build": "vuepress build docs"
     }
   }
   ```

6. **Start writing**
   ```bash
   npm run docs:dev
   ```

VuePress will start a hot-reloading development server at http://localhost:8080.

## Next Steps

- Read the [Configuration Guide](./config.md)
- Learn about [Markdown Extensions](./markdown.md)
- Explore [Plugin Development](./plugin.md)
- Check out [Theme Development](./theme.md)
