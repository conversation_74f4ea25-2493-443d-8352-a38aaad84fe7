{"name": "@vuepress/client", "version": "2.0.0-rc.25", "description": "Client package of VuePress", "keywords": ["vuepress", "client"], "homepage": "https://github.com/vuepress", "bugs": {"url": "https://github.com/vuepress/core/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vuepress/core.git"}, "license": "MIT", "author": "meteorlxy", "type": "module", "exports": {".": "./dist/index.js", "./app": "./dist/app.js", "./package.json": "./package.json", "./templates/*": "./templates/*", "./types": "./types.d.ts"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "templates", "types.d.ts"], "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@vue/devtools-api": "^8.0.1", "@vue/devtools-kit": "^8.0.1", "@vuepress/shared": "workspace:*", "vue": "catalog:", "vue-router": "catalog:"}, "publishConfig": {"access": "public"}, "tsup": {"clean": true, "dts": ["./src/app.ts", "./src/index.ts"], "entry": ["./src/app.ts", "./src/index.ts"], "external": ["@internal/clientConfigs", "@internal/routes", "@internal/siteData"], "format": ["esm"], "outDir": "./dist", "sourcemap": false, "target": "es2023", "tsconfig": "./tsconfig.dts.json"}}