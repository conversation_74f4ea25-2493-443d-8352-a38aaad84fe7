{"name": "@vuepress/bundler-webpack", "version": "2.0.0-rc.25", "description": "Bundler webpack package of VuePress", "keywords": ["vuepress-bundler", "vuepress", "bundler", "webpack"], "homepage": "https://github.com/vuepress", "bugs": {"url": "https://github.com/vuepress/core/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vuepress/core.git"}, "license": "MIT", "author": "meteorlxy", "type": "module", "imports": {"#vuepress-ssr-loader": "./dist/vuepress-ssr-loader.cjs"}, "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@types/express": "^4.17.23", "@types/webpack-env": "^1.18.8", "@vuepress/bundlerutils": "workspace:*", "@vuepress/client": "workspace:*", "@vuepress/core": "workspace:*", "@vuepress/shared": "workspace:*", "@vuepress/utils": "workspace:*", "autoprefixer": "^10.4.21", "copy-webpack-plugin": "^13.0.1", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.2", "esbuild-loader": "~4.3.0", "express": "^4.21.2", "html-webpack-plugin": "^5.6.4", "lightningcss": "^1.30.1", "mini-css-extract-plugin": "^2.9.4", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "style-loader": "^4.0.0", "vue": "catalog:", "vue-loader": "^17.4.2", "vue-router": "catalog:", "webpack": "^5.101.3", "webpack-dev-server": "^5.2.2", "webpack-merge": "^6.0.1", "webpack-v5-chain": "^1.0.0"}, "publishConfig": {"access": "public"}}