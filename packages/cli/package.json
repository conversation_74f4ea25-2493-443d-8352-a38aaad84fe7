{"name": "@vuepress/cli", "version": "2.0.0-rc.25", "description": "CLI package of VuePress", "keywords": ["vuepress", "cli"], "homepage": "https://github.com/vuepress", "bugs": {"url": "https://github.com/vuepress/core/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vuepress/core.git"}, "license": "MIT", "author": "meteorlxy", "type": "module", "exports": {".": "./dist/index.js", "./bin": "./dist/vuepress.js", "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "bin": {"vuepress-cli": "./bin/vuepress.js"}, "files": ["bin", "dist"], "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@vuepress/core": "workspace:*", "@vuepress/shared": "workspace:*", "@vuepress/utils": "workspace:*", "cac": "^6.7.14", "chokidar": "^4.0.3", "envinfo": "^7.14.0", "esbuild": "^0.25.9"}, "devDependencies": {"@types/envinfo": "^7.8.4"}, "publishConfig": {"access": "public"}, "tsup": {"clean": true, "dts": "./src/index.ts", "entry": ["./src/index.ts"], "format": ["esm"], "outDir": "./dist", "sourcemap": false, "target": "es2023", "tsconfig": "../../tsconfig.dts.json"}}