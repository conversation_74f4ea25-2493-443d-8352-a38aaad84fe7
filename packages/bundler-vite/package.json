{"name": "@vuepress/bundler-vite", "version": "2.0.0-rc.25", "description": "Bundler vite package of VuePress", "keywords": ["vuepress-bundler", "vuepress", "bundler", "vite"], "homepage": "https://github.com/vuepress", "bugs": {"url": "https://github.com/vuepress/core/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vuepress/core.git"}, "license": "MIT", "author": "meteorlxy", "type": "module", "exports": {".": "./dist/index.js", "./client": "./client.d.ts", "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "client.d.ts"], "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@vitejs/plugin-vue": "^6.0.1", "@vuepress/bundlerutils": "workspace:*", "@vuepress/client": "workspace:*", "@vuepress/core": "workspace:*", "@vuepress/shared": "workspace:*", "@vuepress/utils": "workspace:*", "autoprefixer": "^10.4.21", "connect-history-api-fallback": "^2.0.0", "postcss": "^8.5.6", "postcss-load-config": "^6.0.1", "rollup": "^4.48.1", "vite": "~7.1.3", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@types/connect-history-api-fallback": "^1.5.4"}, "publishConfig": {"access": "public"}, "tsup": {"clean": true, "dts": "./src/index.ts", "entry": ["./src/index.ts"], "format": ["esm"], "outDir": "./dist", "sourcemap": false, "target": "es2023", "tsconfig": "../../tsconfig.dts.json"}}