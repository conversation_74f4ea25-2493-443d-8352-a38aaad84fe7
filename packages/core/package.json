{"name": "@vuepress/core", "version": "2.0.0-rc.25", "description": "Core package of VuePress", "keywords": ["vuepress", "core"], "homepage": "https://github.com/vuepress", "bugs": {"url": "https://github.com/vuepress/core/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vuepress/core.git"}, "license": "MIT", "author": "meteorlxy", "type": "module", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@vuepress/client": "workspace:*", "@vuepress/markdown": "workspace:*", "@vuepress/shared": "workspace:*", "@vuepress/utils": "workspace:*", "vue": "catalog:"}, "publishConfig": {"access": "public"}, "tsup": {"clean": true, "dts": "./src/index.ts", "entry": ["./src/index.ts"], "format": ["esm"], "outDir": "./dist", "sourcemap": false, "target": "es2023", "tsconfig": "../../tsconfig.dts.json"}}