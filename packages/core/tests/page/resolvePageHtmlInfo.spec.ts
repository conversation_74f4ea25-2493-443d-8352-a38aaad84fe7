import { path } from '@vuepress/utils'
import { describe, expect, it } from 'vitest'
import type { B<PERSON><PERSON> } from '../../src/index.js'
import { createBaseApp, resolvePageHtmlInfo } from '../../src/index.js'

const app = createBaseApp({
  source: path.resolve(__dirname, 'fake-source'),
  theme: { name: 'test' },
  bundler: {} as Bundler,
})

const TEST_CASES: [string, string][] = [
  ['/foo', 'foo.html'],
  ['/foo.html', 'foo.html'],
  ['/foo/bar.html', 'foo/bar.html'],
  ['/foo/bar', 'foo/bar.html'],
  ['/foo/index.html', 'foo/index.html'],
  ['/foo/bar/index.html', 'foo/bar/index.html'],
  ['/foo/', 'foo/index.html'],
  ['/foo/bar/', 'foo/bar/index.html'],
]

describe('should resolve page html file path correctly', () => {
  TEST_CASES.forEach(([source, expected]) => {
    it(JSON.stringify(source), () => {
      expect(
        resolvePageHtmlInfo({
          app,
          path: source,
        }),
      ).toEqual({
        htmlFilePath: app.dir.dest(expected),
        htmlFilePathRelative: expected,
      })
    })
  })
})
