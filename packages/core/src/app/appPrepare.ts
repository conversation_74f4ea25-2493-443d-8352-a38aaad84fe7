import { debug } from '@vuepress/utils'
import type { App } from '../types/index.js'
import {
  prepareClientConfigs,
  preparePageChunk,
  preparePageComponent,
  prepareRoutes,
  prepareSiteData,
} from './prepare/index.js'

const log = debug('vuepress:core/app')

/**
 * Prepare files for development or build
 *
 * - page components
 * - page chunks
 * - routes
 * - site data
 * - other files that generated by plugins
 *
 * @internal
 */
export const appPrepare = async (app: App): Promise<void> => {
  log('prepare start')

  await Promise.all([
    ...app.pages.flatMap((page) => [
      preparePageComponent(app, page),
      preparePageChunk(app, page),
    ]),
    prepareRoutes(app),
    prepareSiteData(app),
    prepareClientConfigs(app),
  ])

  // plugin hook: onPrepared
  await app.pluginApi.hooks.onPrepared.process(app)

  log('prepare finish')
}
