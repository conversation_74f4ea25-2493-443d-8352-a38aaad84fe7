import type { <PERSON><PERSON><PERSON><PERSON>, DefineH<PERSON>, Plugin<PERSON><PERSON> } from '../types/index.js'
import { normalizeAliasDefineHook } from './normalizeAliasDefineHook.js'
import { normalizeClientConfigFileHook } from './normalizeClientConfigFileHook.js'

/**
 * Create registerHooks method for plugin api
 *
 * @internal
 */
export const createPluginApiRegisterHooks =
  (
    plugins: Plugin<PERSON><PERSON>['plugins'],
    hooks: Plugin<PERSON><PERSON>['hooks'],
  ): PluginApi['registerHooks'] =>
  () => {
    plugins.forEach(
      ({
        name: pluginName,
        multiple,

        alias,
        define,
        clientConfigFile,

        ...commonHooks
      }) => {
        /**
         * hooks that need to be normalized
         */
        if (alias) {
          hooks.alias.add({
            pluginName,
            hook: normalizeAliasDefineHook<AliasHook>(alias),
          })
        }

        if (define) {
          hooks.define.add({
            pluginName,
            hook: normalizeAliasDefineHook<DefineHook>(define),
          })
        }

        if (clientConfigFile) {
          hooks.clientConfigFile.add({
            pluginName,
            hook: normalizeClientConfigFileHook(clientConfigFile),
          })
        }

        /**
         * common hooks
         */
        Object.keys(commonHooks).forEach((key) => {
          if (hooks[key] && commonHooks[key]) {
            hooks[key as keyof typeof hooks].add({
              pluginName,
              // @ts-expect-error: the type could not be narrowed correctly
              hook: commonHooks[key as keyof typeof commonHooks],
            })
          }
        })
      },
    )
  }
