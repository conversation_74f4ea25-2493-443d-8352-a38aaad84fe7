// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compatibility with otherPlugin > should preserve the things after code as fence info 1`] = `
"<pre><code class="language-js{1,3-4}">const msg = 'hello from js'

console.log(msg)

console.log('foo bar')
</code></pre>
"
`;

exports[`compatibility with otherPlugin > should preserve the things after code as fence info 2`] = `
"<pre><code class="language-md:no-line-numbers:no-v-pre"># msg

hello from md

## foo bar
</code></pre>
"
`;
