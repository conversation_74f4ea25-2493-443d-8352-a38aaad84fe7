// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`:v-pre / :no-v-pre > should work if \`block\` is disabled 1`] = `
"<pre v-pre><code class="language-js:v-pre">const a = 1
</code></pre>
<pre><code class="language-js:no-v-pre">const a = 1
</code></pre>
<pre v-pre><code class="language-js{1,2}:v-pre">const a = 1
</code></pre>
<pre><code class="language-js{1,2}:no-v-pre">const a = 1
</code></pre>
<pre v-pre><code class="language-js:other-syntax:v-pre">const a = 1
</code></pre>
<pre><code class="language-js:other-syntax:no-v-pre">const a = 1
</code></pre>
<pre><code class="language-js">const a = 1
</code></pre>
"
`;

exports[`:v-pre / :no-v-pre > should work if \`block\` is enabled by default 1`] = `
"<pre v-pre><code class="language-js:v-pre">const a = 1
</code></pre>
<pre><code class="language-js:no-v-pre">const a = 1
</code></pre>
<pre v-pre><code class="language-js{1,2}:v-pre">const a = 1
</code></pre>
<pre><code class="language-js{1,2}:no-v-pre">const a = 1
</code></pre>
<pre v-pre><code class="language-js:other-syntax:v-pre">const a = 1
</code></pre>
<pre><code class="language-js:other-syntax:no-v-pre">const a = 1
</code></pre>
<pre v-pre><code class="language-js">const a = 1
</code></pre>
"
`;

exports[`plugin options > should disable \`block\` 1`] = `
"<pre><code class="language-js">const a = 1
</code></pre>
<p><code v-pre>inline</code></p>
"
`;

exports[`plugin options > should disable \`block\` and \`inline\` 1`] = `
"<pre><code class="language-js">const a = 1
</code></pre>
<p><code>inline</code></p>
"
`;

exports[`plugin options > should disable \`inline\` 1`] = `
"<pre v-pre><code class="language-js">const a = 1
</code></pre>
<p><code>inline</code></p>
"
`;

exports[`plugin options > should process code with default options 1`] = `
"<pre v-pre><code class="language-js">const a = 1
</code></pre>
<p><code v-pre>inline</code></p>
"
`;

exports[`syntax highlighting > should work highlighted code is wrapped with \`<pre>\` 1`] = `
"<pre v-pre><code>highlighted code: const a = 1
, lang: js:v-pre</code></pre>
<pre><code>highlighted code: const a = 1
, lang: js:no-v-pre</code></pre>
<pre v-pre><code>highlighted code: const a = 1
, lang: js{1,2}:v-pre</code></pre>
<pre><code>highlighted code: const a = 1
, lang: js{1,2}:no-v-pre</code></pre>
<pre v-pre><code>highlighted code: const a = 1
, lang: js:other-syntax:v-pre</code></pre>
<pre><code>highlighted code: const a = 1
, lang: js:other-syntax:no-v-pre</code></pre>
<pre v-pre><code>highlighted code: const a = 1
, lang: js</code></pre>
"
`;

exports[`syntax highlighting > should work if highlighted code is not wrapped with \`<pre>\` 1`] = `
"<pre v-pre><code class="language-js:v-pre">highlighted code: const a = 1
, lang: js:v-pre</code></pre>
<pre><code class="language-js:no-v-pre">highlighted code: const a = 1
, lang: js:no-v-pre</code></pre>
<pre v-pre><code class="language-js{1,2}:v-pre">highlighted code: const a = 1
, lang: js{1,2}:v-pre</code></pre>
<pre><code class="language-js{1,2}:no-v-pre">highlighted code: const a = 1
, lang: js{1,2}:no-v-pre</code></pre>
<pre v-pre><code class="language-js:other-syntax:v-pre">highlighted code: const a = 1
, lang: js:other-syntax:v-pre</code></pre>
<pre><code class="language-js:other-syntax:no-v-pre">highlighted code: const a = 1
, lang: js:other-syntax:no-v-pre</code></pre>
<pre v-pre><code class="language-js">highlighted code: const a = 1
, lang: js</code></pre>
"
`;
