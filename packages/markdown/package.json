{"name": "@vuepress/markdown", "version": "2.0.0-rc.25", "description": "Markdown package of VuePress", "keywords": ["vuepress", "markdown"], "homepage": "https://github.com/vuepress", "bugs": {"url": "https://github.com/vuepress/core/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vuepress/core.git"}, "license": "MIT", "author": "meteorlxy", "type": "module", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@mdit-vue/plugin-component": "^3.0.2", "@mdit-vue/plugin-frontmatter": "^3.0.2", "@mdit-vue/plugin-headers": "^3.0.2", "@mdit-vue/plugin-sfc": "^3.0.2", "@mdit-vue/plugin-title": "^3.0.2", "@mdit-vue/plugin-toc": "^3.0.2", "@mdit-vue/shared": "^3.0.2", "@mdit-vue/types": "^3.0.2", "@types/markdown-it": "^14.1.2", "@types/markdown-it-emoji": "^3.0.1", "@vuepress/shared": "workspace:*", "@vuepress/utils": "workspace:*", "markdown-it": "^14.1.0", "markdown-it-anchor": "^9.2.0", "markdown-it-emoji": "^3.0.0", "mdurl": "^2.0.0"}, "devDependencies": {"@types/mdurl": "^2.0.0"}, "publishConfig": {"access": "public"}, "tsup": {"clean": true, "dts": "./src/index.ts", "entry": ["./src/index.ts"], "format": ["esm"], "outDir": "./dist", "sourcemap": false, "target": "es2023", "tsconfig": "../../tsconfig.dts.json"}}