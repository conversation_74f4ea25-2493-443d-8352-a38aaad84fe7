{"name": "@vuepress/utils", "version": "2.0.0-rc.25", "description": "Utils package of VuePress", "keywords": ["vuepress", "utils"], "homepage": "https://github.com/vuepress", "bugs": {"url": "https://github.com/vuepress/core/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vuepress/core.git"}, "license": "MIT", "author": "meteorlxy", "type": "module", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@types/debug": "^4.1.12", "@types/fs-extra": "^11.0.4", "@types/hash-sum": "^1.0.2", "@types/picomatch": "^4.0.2", "@vuepress/shared": "workspace:*", "debug": "^4.4.1", "fs-extra": "^11.3.1", "hash-sum": "^2.0.0", "ora": "^8.2.0", "picocolors": "^1.1.1", "picomatch": "^4.0.3", "tinyglobby": "^0.2.14", "upath": "^2.0.1"}, "publishConfig": {"access": "public"}, "tsup": {"clean": true, "dts": "./src/index.ts", "entry": ["./src/index.ts"], "format": ["esm"], "outDir": "./dist", "sourcemap": false, "target": "es2023", "tsconfig": "../../tsconfig.dts.json"}}