{"name": "vuepress", "version": "2.0.0-rc.25", "description": "Vue-powered Static Site Generator", "keywords": ["documentation", "generator", "vue", "vuepress"], "homepage": "https://github.com/vuepress", "bugs": {"url": "https://github.com/vuepress/core/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vuepress/core.git"}, "license": "MIT", "author": "meteorlxy", "type": "module", "exports": {".": "./dist/index.js", "./bin": "./bin/vuepress.js", "./cli": "./dist/cli.js", "./client": "./dist/client.js", "./client-app": "./dist/client-app.js", "./client-types": "./client-types.d.ts", "./core": "./dist/core.js", "./markdown": "./dist/markdown.js", "./shared": "./dist/shared.js", "./utils": "./dist/utils.js", "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "bin": {"vuepress": "./bin/vuepress.js", "vuepress-vite": "./bin/vuepress-vite.js", "vuepress-webpack": "./bin/vuepress-webpack.js"}, "files": ["bin", "dist", "client-types.d.ts"], "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@vuepress/cli": "workspace:*", "@vuepress/client": "workspace:*", "@vuepress/core": "workspace:*", "@vuepress/markdown": "workspace:*", "@vuepress/shared": "workspace:*", "@vuepress/utils": "workspace:*", "vue": "catalog:"}, "peerDependencies": {"@vuepress/bundler-vite": "workspace:*", "@vuepress/bundler-webpack": "workspace:*", "vue": "catalog:"}, "peerDependenciesMeta": {"@vuepress/bundler-vite": {"optional": true}, "@vuepress/bundler-webpack": {"optional": true}}, "engines": {"node": "^20.9.0 || >=22.0.0"}, "tsup": {"clean": true, "dts": ["./src/index.ts", "./src/cli.ts", "./src/client-app.ts", "./src/client.ts", "./src/core.ts", "./src/markdown.ts", "./src/shared.ts", "./src/utils.ts"], "entry": ["./src/index.ts", "./src/cli.ts", "./src/client-app.ts", "./src/client.ts", "./src/core.ts", "./src/markdown.ts", "./src/shared.ts", "./src/utils.ts"], "format": ["esm"], "outDir": "./dist", "sourcemap": false, "target": "es2023", "tsconfig": "../../tsconfig.dts.json"}}