{"extends": "./tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "declaration": false, "jsx": "preserve", "paths": {"@internal/*": ["./packages/client/src/types/internal/*.d.ts"], "@vuepress/*": ["./packages/*/src"], "vuepress": ["./packages/vuepress/src"]}, "types": ["webpack-env", "vite/client"]}, "include": ["**/.vuepress/**/*", "e2e/**/*", "packages/**/*", "scripts/**/*", "*.config.ts"], "exclude": ["node_modules", ".temp", "lib", "dist"]}