{"name": "@vuepress/monorepo", "version": "2.0.0-rc.25", "private": true, "type": "module", "scripts": {"build": "pnpm -r --workspace-concurrency=1 --stream build", "check-types": "vue-tsc --noEmit", "clean": "pnpm --parallel --stream clean", "format": "prettier --write .", "lint": "eslint . && prettier --check .", "lint:fix": "eslint --fix . && prettier --write .", "prepare": "husky", "release": "pnpm release:check && pnpm release:version && pnpm release:publish", "release:changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "release:check": "pnpm clean && pnpm build && pnpm check-types && pnpm lint && pnpm test", "release:publish": "pnpm -r publish --tag next", "release:version": "bumpp -r --execute=\"pnpm release:changelog\" --commit \"build: publish v%s\" --all", "test": "pnpm test:unit && pnpm test:e2e", "test:e2e": "pnpm --filter e2e e2e:dev && pnpm --filter e2e e2e:build", "test:e2e:webpack": "pnpm --filter e2e e2e:dev:webpack && pnpm --filter e2e e2e:build:webpack", "test:unit": "vitest run", "test:unit:cov": "vitest run --coverage"}, "lint-staged": {"*.!(cjs|js|ts|vue)": "prettier --write --ignore-unknown", "*.(cjs|js|ts|vue)": ["eslint --fix", "prettier --write"], "package.json": "sort-package-json"}, "prettier": "prettier-config-vuepress", "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/types": "^19.8.1", "@types/node": "^24.3.0", "@types/webpack-env": "^1.18.8", "@vitest/coverage-istanbul": "^3.2.4", "bumpp": "^10.2.3", "conventional-changelog-cli": "^5.0.0", "eslint": "^9.34.0", "eslint-config-vuepress": "^7.0.4", "husky": "^9.1.7", "jiti": "^2.5.1", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "prettier-config-vuepress": "^7.0.4", "rimraf": "^6.0.1", "sort-package-json": "^3.4.0", "taze": "^19.3.0", "tsconfig-vuepress": "^7.0.0", "tsup": "^8.5.0", "typescript": "~5.9.2", "vite": "~7.1.3", "vitest": "^3.2.4", "vue-tsc": "^3.0.6"}, "packageManager": "pnpm@10.15.0", "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "esbuild", "unrs-resolver"]}}