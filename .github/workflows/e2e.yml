name: e2e

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
  workflow_dispatch:

jobs:
  e2e:
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node: ['20', '22', '24']
        bundler: ['webpack', 'vite']

    runs-on: ${{ matrix.os }}

    steps:
      - uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Use Node.js ${{ matrix.node }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install playwright chromium
        working-directory: ./e2e
        run: pnpm playwright install chromium

      - name: Build source files
        run: pnpm build

      - name: E2E test (base /)
        run: pnpm test:e2e
        env:
          E2E_BASE: /
          E2E_BUNDLER: ${{ matrix.bundler }}

      - name: E2E test (base /e2e/)
        run: pnpm test:e2e
        env:
          E2E_BASE: /e2e/
          E2E_BUNDLER: ${{ matrix.bundler }}

  e2e-result:
    if: ${{ !cancelled() }}
    name: e2e result
    runs-on: ubuntu-latest
    needs: [e2e]
    steps:
      - if: ${{ contains(needs.*.result, 'failure') || contains(needs.*.result, 'cancelled') }}
        run: exit 1
