name: coverage

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

on:
  push:
    branches:
      - main
    paths-ignore:
      - e2e/**
      - '**.md'
  pull_request:
    branches:
      - main
  workflow_dispatch:

jobs:
  coverage:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          cache: pnpm
          node-version: lts/*

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build source code
        run: pnpm build

      - name: Unit test coverage
        run: pnpm test:unit:cov

      - name: Coveralls
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
