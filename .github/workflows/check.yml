name: check

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
  workflow_dispatch:

jobs:
  check:
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node: ['20', '22', '24']

    runs-on: ${{ matrix.os }}

    steps:
      - uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Use Node.js ${{ matrix.node }}
        uses: actions/setup-node@v4
        with:
          cache: pnpm
          node-version: ${{ matrix.node }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build
        run: pnpm build

      - name: Check Types
        run: pnpm check-types

      - name: Lint
        run: pnpm lint

      - name: Unit test
        run: pnpm test:unit

  check-result:
    if: ${{ always() }}
    name: check result
    runs-on: ubuntu-latest
    needs: [check]
    steps:
      - if: ${{ contains(needs.*.result, 'failure') || contains(needs.*.result, 'cancelled') }}
        run: exit 1
